const axios = require('axios');

// Test the authentication flow
async function testAuthFlow() {
  console.log('🧪 Testing Authentication Flow...\n');

  try {
    // Test 1: Login with existing user
    console.log('1️⃣ Testing login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    }, {
      withCredentials: true,
      validateStatus: () => true // Don't throw on non-2xx status
    });

    if (loginResponse.status === 200) {
      console.log('✅ Login successful');
      
      // Extract cookies from login response
      const cookies = loginResponse.headers['set-cookie'];
      console.log('🍪 Received cookies:', cookies ? 'Yes' : 'No');

      if (cookies) {
        // Test 2: Check profile with cookies
        console.log('\n2️⃣ Testing profile access with cookies...');
        const profileResponse = await axios.get('http://localhost:3001/api/auth/profile', {
          headers: {
            'Cookie': cookies.join('; ')
          },
          validateStatus: () => true
        });

        if (profileResponse.status === 200) {
          console.log('✅ Profile access successful');
          console.log('👤 User:', profileResponse.data.data.user.email);
        } else {
          console.log('❌ Profile access failed:', profileResponse.status, profileResponse.data);
        }

        // Test 3: Logout
        console.log('\n3️⃣ Testing logout...');
        const logoutResponse = await axios.post('http://localhost:3001/api/auth/logout', {}, {
          headers: {
            'Cookie': cookies.join('; ')
          },
          validateStatus: () => true
        });

        if (logoutResponse.status === 200) {
          console.log('✅ Logout successful');
        } else {
          console.log('❌ Logout failed:', logoutResponse.status, logoutResponse.data);
        }
      }
    } else {
      console.log('❌ Login failed:', loginResponse.status, loginResponse.data);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }

  console.log('\n🏁 Authentication flow test completed');
}

testAuthFlow();

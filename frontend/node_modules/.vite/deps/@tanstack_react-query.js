import {
  CancelledError,
  HydrationBoundary,
  InfiniteQueryObserver,
  IsRestoringProvider,
  Mutation,
  MutationCache,
  MutationObserver,
  QueriesObserver,
  Query,
  QueryCache,
  QueryClient,
  QueryClientContext,
  QueryClientProvider,
  QueryErrorResetBoundary,
  QueryObserver,
  dataTagErrorSymbol,
  dataTagSymbol,
  defaultScheduler,
  defaultShouldDehydrateMutation,
  defaultShouldDehydrateQuery,
  dehydrate,
  focusManager,
  hashKey,
  hydrate,
  infiniteQueryOptions,
  isCancelledError,
  isServer,
  keepPreviousData,
  matchMutation,
  matchQuery,
  noop,
  notifyManager,
  onlineManager,
  partialMatchKey,
  queryOptions,
  replaceEqualDeep,
  shouldThrowError,
  skipToken,
  streamedQuery,
  unsetMarker,
  useInfiniteQuery,
  useIsFetching,
  useIsMutating,
  useIsRestoring,
  useMutation,
  useMutationState,
  usePrefetchInfiniteQuery,
  usePrefetchQuery,
  useQueries,
  useQuery,
  useQueryClient,
  useQueryErrorResetBoundary,
  useSuspenseInfiniteQuery,
  useSuspenseQueries,
  useSuspenseQuery
} from "./chunk-GCUUZQOP.js";
import "./chunk-QLG26ATY.js";
import "./chunk-OHY7G46N.js";
import "./chunk-WOOG5QLI.js";
export {
  CancelledError,
  HydrationBoundary,
  InfiniteQueryObserver,
  IsRestoringProvider,
  Mutation,
  MutationCache,
  MutationObserver,
  QueriesObserver,
  Query,
  QueryCache,
  QueryClient,
  QueryClientContext,
  QueryClientProvider,
  QueryErrorResetBoundary,
  QueryObserver,
  dataTagErrorSymbol,
  dataTagSymbol,
  defaultScheduler,
  defaultShouldDehydrateMutation,
  defaultShouldDehydrateQuery,
  dehydrate,
  streamedQuery as experimental_streamedQuery,
  focusManager,
  hashKey,
  hydrate,
  infiniteQueryOptions,
  isCancelledError,
  isServer,
  keepPreviousData,
  matchMutation,
  matchQuery,
  noop,
  notifyManager,
  onlineManager,
  partialMatchKey,
  queryOptions,
  replaceEqualDeep,
  shouldThrowError,
  skipToken,
  unsetMarker,
  useInfiniteQuery,
  useIsFetching,
  useIsMutating,
  useIsRestoring,
  useMutation,
  useMutationState,
  usePrefetchInfiniteQuery,
  usePrefetchQuery,
  useQueries,
  useQuery,
  useQueryClient,
  useQueryErrorResetBoundary,
  useSuspenseInfiniteQuery,
  useSuspenseQueries,
  useSuspenseQuery
};
//# sourceMappingURL=@tanstack_react-query.js.map

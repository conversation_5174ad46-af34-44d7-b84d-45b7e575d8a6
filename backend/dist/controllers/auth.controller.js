"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshToken = exports.logout = exports.getProfile = exports.login = exports.register = void 0;
const bookmarked_types_1 = require("bookmarked-types");
const user_service_1 = require("../services/user.service");
const auth_service_1 = require("../services/auth.service");
const jwt_1 = require("../utils/jwt");
const error_middleware_1 = require("../middleware/error.middleware");
exports.register = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const data = req.body;
    const result = await auth_service_1.authService.registerUser(data);
    (0, jwt_1.setAuthCookies)(res, result.token);
    const response = {
        success: true,
        message: "User registered successfully",
        data: {
            user: result.user,
        },
        timestamp: new Date().toISOString(),
    };
    res.status(bookmarked_types_1.HttpStatus.CREATED).json(response);
});
exports.login = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const data = req.body;
    const result = await auth_service_1.authService.loginUser(data);
    (0, jwt_1.setAuthCookies)(res, result.token);
    const response = {
        success: true,
        message: "Login successful",
        data: {
            user: result.user,
        },
        timestamp: new Date().toISOString(),
    };
    res.status(bookmarked_types_1.HttpStatus.OK).json(response);
});
exports.getProfile = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw new error_middleware_1.ApiError("User not authenticated", bookmarked_types_1.HttpStatus.UNAUTHORIZED, bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR);
    }
    const user = await user_service_1.userService.getUserProfile(req.user.userId);
    const response = {
        success: true,
        message: "Profile retrieved successfully",
        data: {
            user,
        },
        timestamp: new Date().toISOString(),
    };
    res.status(bookmarked_types_1.HttpStatus.OK).json(response);
});
exports.logout = (0, error_middleware_1.asyncHandler)(async (_req, res) => {
    (0, jwt_1.clearAuthCookies)(res);
    const response = {
        success: true,
        message: "Logout successful",
        timestamp: new Date().toISOString(),
    };
    res.status(bookmarked_types_1.HttpStatus.OK).json(response);
});
exports.refreshToken = (0, error_middleware_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw new error_middleware_1.ApiError("User not authenticated", bookmarked_types_1.HttpStatus.UNAUTHORIZED, bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR);
    }
    const token = (0, jwt_1.generateToken)({
        userId: req.user.userId,
        email: req.user.email,
    });
    (0, jwt_1.setAuthCookies)(res, token);
    const response = {
        success: true,
        message: "Token refreshed successfully",
        timestamp: new Date().toISOString(),
    };
    res.status(bookmarked_types_1.HttpStatus.OK).json(response);
});
//# sourceMappingURL=auth.controller.js.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const environment_1 = require("./config/environment");
const error_middleware_1 = require("./middleware/error.middleware");
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
const bookmarked_types_1 = require("bookmarked-types");
const app = (0, express_1.default)();
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" },
}));
app.use((0, cors_1.default)(environment_1.config.cors));
const limiter = (0, express_rate_limit_1.default)(environment_1.config.rateLimit);
app.use("/api/", limiter);
app.use(express_1.default.json({ limit: "10mb" }));
app.use(express_1.default.urlencoded({ extended: true, limit: "10mb" }));
app.use((0, cookie_parser_1.default)());
app.use((0, compression_1.default)());
if (environment_1.config.app.isDevelopment) {
    app.use((0, morgan_1.default)("dev"));
}
else {
    app.use((0, morgan_1.default)("combined"));
}
app.use("/api/auth", auth_routes_1.default);
app.use("/api/users", user_routes_1.default);
app.get("/", (_req, res) => {
    const response = {
        success: true,
        message: "Welcome to Bookmarked API",
        data: {
            name: environment_1.config.app.name,
            version: environment_1.config.app.version,
            environment: environment_1.config.app.env,
            documentation: "/api/docs",
        },
        timestamp: new Date().toISOString(),
    };
    res.status(bookmarked_types_1.HttpStatus.OK).json(response);
});
app.use("*", error_middleware_1.notFoundHandler);
app.use(error_middleware_1.errorHandler);
exports.default = app;
//# sourceMappingURL=app.js.map
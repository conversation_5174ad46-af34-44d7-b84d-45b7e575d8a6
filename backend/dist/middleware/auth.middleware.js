"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireOwnership = exports.authorize = exports.authenticate = void 0;
const bookmarked_types_1 = require("bookmarked-types");
const jwt_1 = require("../utils/jwt");
const authenticate = (req, res, next) => {
    try {
        const token = (0, jwt_1.extractTokenFromRequest)(req);
        if (!token) {
            const response = {
                success: false,
                message: "Access token required",
                error: {
                    code: bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR,
                    message: "No access token provided",
                },
                timestamp: new Date().toISOString(),
            };
            return res.status(bookmarked_types_1.HttpStatus.UNAUTHORIZED).json(response);
        }
        const decoded = (0, jwt_1.verifyToken)(token);
        req.user = decoded;
        return next();
    }
    catch (error) {
        const response = {
            success: false,
            message: "Invalid or expired token",
            error: {
                code: bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR,
                message: error instanceof Error ? error.message : "Token verification failed",
            },
            timestamp: new Date().toISOString(),
        };
        return res.status(bookmarked_types_1.HttpStatus.UNAUTHORIZED).json(response);
    }
};
exports.authenticate = authenticate;
const authorize = (requiredRoles = []) => {
    return (req, res, next) => {
        if (!req.user) {
            const response = {
                success: false,
                message: "Authentication required",
                error: {
                    code: bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR,
                    message: "User not authenticated",
                },
                timestamp: new Date().toISOString(),
            };
            return res.status(bookmarked_types_1.HttpStatus.UNAUTHORIZED).json(response);
        }
        if (requiredRoles.length > 0) {
            console.log("Role checking not implemented yet:", requiredRoles);
        }
        return next();
    };
};
exports.authorize = authorize;
const requireOwnership = (userIdField = "userId") => {
    return (req, res, next) => {
        if (!req.user) {
            const response = {
                success: false,
                message: "Authentication required",
                error: {
                    code: bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR,
                    message: "User not authenticated",
                },
                timestamp: new Date().toISOString(),
            };
            return res.status(bookmarked_types_1.HttpStatus.UNAUTHORIZED).json(response);
        }
        const resourceUserId = req.params[userIdField] ||
            req.body[userIdField] ||
            req.query[userIdField];
        if (resourceUserId && resourceUserId !== req.user.userId) {
            const response = {
                success: false,
                message: "Access denied",
                error: {
                    code: bookmarked_types_1.ErrorCodes.AUTHORIZATION_ERROR,
                    message: "You can only access your own resources",
                },
                timestamp: new Date().toISOString(),
            };
            return res.status(bookmarked_types_1.HttpStatus.FORBIDDEN).json(response);
        }
        return next();
    };
};
exports.requireOwnership = requireOwnership;
//# sourceMappingURL=auth.middleware.js.map
import { Request, Response } from "express";
import type { TokenPayload } from "bookmarked-types";
export declare const COOKIE_CONFIG: {
    readonly ACCESS_TOKEN: "accessToken";
    readonly REFRESH_TOKEN: "refreshToken";
    readonly OPTIONS: {
        readonly httpOnly: true;
        readonly secure: boolean;
        readonly sameSite: "lax";
        readonly maxAge: number;
        readonly path: "/";
    };
    readonly REFRESH_OPTIONS: {
        readonly httpOnly: true;
        readonly secure: boolean;
        readonly sameSite: "lax";
        readonly maxAge: number;
        readonly path: "/api/auth/refresh";
    };
};
export declare const generateToken: (payload: Omit<TokenPayload, "iat" | "exp">) => string;
export declare const verifyToken: (token: string) => TokenPayload;
export declare const decodeToken: (token: string) => TokenPayload | null;
export declare const extractTokenFromHeader: (authHeader: string | undefined) => string | null;
export declare const extractTokenFromCookies: (cookies: Record<string, string>) => string | null;
export declare const extractTokenFromRequest: (req: Request) => string | null;
export declare const setAuthCookies: (res: Response, accessToken: string, refreshToken?: string) => void;
export declare const clearAuthCookies: (res: Response) => void;
export declare const isTokenExpired: (token: string) => boolean;
export declare const getTokenExpiration: (token: string) => Date | null;
//# sourceMappingURL=jwt.d.ts.map
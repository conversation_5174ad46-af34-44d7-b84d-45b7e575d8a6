"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTokenExpiration = exports.isTokenExpired = exports.clearAuthCookies = exports.setAuthCookies = exports.extractTokenFromRequest = exports.extractTokenFromCookies = exports.extractTokenFromHeader = exports.decodeToken = exports.verifyToken = exports.generateToken = exports.COOKIE_CONFIG = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const environment_1 = require("../config/environment");
exports.COOKIE_CONFIG = {
    ACCESS_TOKEN: "accessToken",
    REFRESH_TOKEN: "refreshToken",
    OPTIONS: {
        httpOnly: true,
        secure: environment_1.config.app.isProduction,
        sameSite: "lax",
        maxAge: 24 * 60 * 60 * 1000,
        path: "/",
    },
    REFRESH_OPTIONS: {
        httpOnly: true,
        secure: environment_1.config.app.isProduction,
        sameSite: "lax",
        maxAge: 7 * 24 * 60 * 60 * 1000,
        path: "/api/auth/refresh",
    },
};
const generateToken = (payload) => {
    return jsonwebtoken_1.default.sign(payload, environment_1.config.auth.jwtSecret, {
        expiresIn: environment_1.config.auth.jwtExpiresIn,
    });
};
exports.generateToken = generateToken;
const verifyToken = (token) => {
    try {
        return jsonwebtoken_1.default.verify(token, environment_1.config.auth.jwtSecret);
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            throw new Error("Invalid token");
        }
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            throw new Error("Token expired");
        }
        throw new Error("Token verification failed");
    }
};
exports.verifyToken = verifyToken;
const decodeToken = (token) => {
    try {
        return jsonwebtoken_1.default.decode(token);
    }
    catch (error) {
        return null;
    }
};
exports.decodeToken = decodeToken;
const extractTokenFromHeader = (authHeader) => {
    if (!authHeader) {
        return null;
    }
    const parts = authHeader.split(" ");
    if (parts.length !== 2 || parts[0] !== "Bearer") {
        return null;
    }
    return parts[1] || null;
};
exports.extractTokenFromHeader = extractTokenFromHeader;
const extractTokenFromCookies = (cookies) => {
    return cookies[exports.COOKIE_CONFIG.ACCESS_TOKEN] || null;
};
exports.extractTokenFromCookies = extractTokenFromCookies;
const extractTokenFromRequest = (req) => {
    const tokenFromCookie = (0, exports.extractTokenFromCookies)(req.cookies || {});
    if (tokenFromCookie) {
        return tokenFromCookie;
    }
    return (0, exports.extractTokenFromHeader)(req.headers.authorization);
};
exports.extractTokenFromRequest = extractTokenFromRequest;
const setAuthCookies = (res, accessToken, refreshToken) => {
    res.cookie(exports.COOKIE_CONFIG.ACCESS_TOKEN, accessToken, exports.COOKIE_CONFIG.OPTIONS);
    if (refreshToken) {
        res.cookie(exports.COOKIE_CONFIG.REFRESH_TOKEN, refreshToken, exports.COOKIE_CONFIG.REFRESH_OPTIONS);
    }
};
exports.setAuthCookies = setAuthCookies;
const clearAuthCookies = (res) => {
    res.clearCookie(exports.COOKIE_CONFIG.ACCESS_TOKEN, {
        path: exports.COOKIE_CONFIG.OPTIONS.path,
        httpOnly: true,
        secure: environment_1.config.app.isProduction,
        sameSite: "lax",
    });
    res.clearCookie(exports.COOKIE_CONFIG.REFRESH_TOKEN, {
        path: exports.COOKIE_CONFIG.REFRESH_OPTIONS.path,
        httpOnly: true,
        secure: environment_1.config.app.isProduction,
        sameSite: "lax",
    });
};
exports.clearAuthCookies = clearAuthCookies;
const isTokenExpired = (token) => {
    try {
        const decoded = (0, exports.decodeToken)(token);
        if (!decoded || !decoded.exp) {
            return true;
        }
        const currentTime = Math.floor(Date.now() / 1000);
        return decoded.exp < currentTime;
    }
    catch (error) {
        return true;
    }
};
exports.isTokenExpired = isTokenExpired;
const getTokenExpiration = (token) => {
    try {
        const decoded = (0, exports.decodeToken)(token);
        if (!decoded || !decoded.exp) {
            return null;
        }
        return new Date(decoded.exp * 1000);
    }
    catch (error) {
        return null;
    }
};
exports.getTokenExpiration = getTokenExpiration;
//# sourceMappingURL=jwt.js.map